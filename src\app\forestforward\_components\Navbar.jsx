"use client";

import { Button, useMediaQuery } from "@relume_io/relume-ui";
import { AnimatePresence, motion } from "framer-motion";
import React, { useState } from "react";
import { RxChevronDown } from "react-icons/rx";

const useRelume = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const isMobile = useMediaQuery("(max-width: 991px)");
  const toggleMobileMenu = () => setIsMobileMenuOpen((prev) => !prev);
  const openOnMobileDropdownMenu = () => {
    setIsDropdownOpen((prev) => !prev);
  };
  const openOnDesktopDropdownMenu = () => {
    !isMobile && setIsDropdownOpen(true);
  };
  const closeOnDesktopDropdownMenu = () => {
    !isMobile && setIsDropdownOpen(false);
  };
  const animateMobileMenu = isMobileMenuOpen ? "open" : "close";
  const animateMobileMenuButtonSpan = isMobileMenuOpen
    ? ["open", "rotatePhase"]
    : "closed";
  const animateDropdownMenu = isDropdownOpen ? "open" : "close";
  const animateDropdownMenuIcon = isDropdownOpen ? "rotated" : "initial";
  return {
    toggleMobileMenu,
    openOnDesktopDropdownMenu,
    closeOnDesktopDropdownMenu,
    openOnMobileDropdownMenu,
    animateMobileMenu,
    animateMobileMenuButtonSpan,
    animateDropdownMenu,
    animateDropdownMenuIcon,
  };
};

export function Navbar2() {
  const useActive = useRelume();
  return (
    <section
      id="relume"
      className="z-[999] flex w-full items-center border-b border-border-primary bg-background lg:min-h-18 lg:px-[5%]"
    >
      <div className="mx-auto size-full lg:grid lg:grid-cols-[0.375fr_1fr_0.375fr] lg:items-center lg:justify-between lg:gap-4">
        <div className="flex min-h-16 items-center justify-between px-[5%] md:min-h-18 lg:min-h-full lg:px-0">
          <a href="#">
            <img
              src="https://d22po4pjz3o32e.cloudfront.net/logo-image.svg"
              alt="Logo image"
            />
          </a>
          <div className="flex items-center gap-4 lg:hidden">
            <div>
              <Button
                className="w-full px-4 py-1"
                title="Let's talk!"
                size="sm"
              >
                Let's talk!
              </Button>
            </div>
            <button
              className="-mr-2 flex size-12 flex-col items-center justify-center lg:hidden"
              onClick={useActive.toggleMobileMenu}
            >
              <motion.span
                className="my-[3px] h-0.5 w-6 bg-text-primary"
                animate={useActive.animateMobileMenuButtonSpan}
                variants={{
                  open: { translateY: 8, transition: { delay: 0.1 } },
                  rotatePhase: { rotate: -45, transition: { delay: 0.2 } },
                  closed: {
                    translateY: 0,
                    rotate: 0,
                    transition: { duration: 0.2 },
                  },
                }}
              />
              <motion.span
                className="my-[3px] h-0.5 w-6 bg-text-primary"
                animate={useActive.animateMobileMenu}
                variants={{
                  open: { width: 0, transition: { duration: 0.1 } },
                  closed: {
                    width: "1.5rem",
                    transition: { delay: 0.3, duration: 0.2 },
                  },
                }}
              />
              <motion.span
                className="my-[3px] h-0.5 w-6 bg-text-primary"
                animate={useActive.animateMobileMenuButtonSpan}
                variants={{
                  open: { translateY: -8, transition: { delay: 0.1 } },
                  rotatePhase: { rotate: 45, transition: { delay: 0.2 } },
                  closed: {
                    translateY: 0,
                    rotate: 0,
                    transition: { duration: 0.2 },
                  },
                }}
              />
            </button>
          </div>
        </div>
        <motion.div
          variants={{
            open: { height: "var(--height-open, 100dvh)" },
            close: { height: "var(--height-closed, 0)" },
          }}
          animate={useActive.animateMobileMenu}
          initial="close"
          exit="close"
          transition={{ duration: 0.4 }}
          className="overflow-hidden px-[5%] text-center lg:flex lg:items-center lg:justify-center lg:px-0 lg:[--height-closed:auto] lg:[--height-open:auto]"
        >
          <div
            onMouseEnter={useActive.openOnDesktopDropdownMenu}
            onMouseLeave={useActive.closeOnDesktopDropdownMenu}
          >
            <button
              className="flex w-full items-center justify-center gap-4 py-3 text-center text-md lg:w-auto lg:flex-none lg:justify-start lg:gap-2 lg:px-4 lg:py-2 lg:text-base text-text-primary hover:text-link transition-colors"
              onClick={useActive.openOnMobileDropdownMenu}
            >
              <span>Diensten</span>
              <motion.span
                variants={{ rotated: { rotate: 180 }, initial: { rotate: 0 } }}
                animate={useActive.animateDropdownMenuIcon}
                transition={{ duration: 0.3 }}
              >
                <RxChevronDown />
              </motion.span>
            </button>
            <AnimatePresence>
              <motion.nav
                variants={{
                  open: {
                    visibility: "visible",
                    opacity: "var(--opacity-open, 100%)",
                    display: "block",
                    y: 0,
                  },
                  close: {
                    visibility: "hidden",
                    opacity: "var(--opacity-close, 0)",
                    display: "none",
                    y: "var(--y-close, 0%)",
                  },
                }}
                animate={useActive.animateDropdownMenu}
                initial="close"
                exit="close"
                transition={{ duration: 0.2 }}
                className="bg-background border border-border-primary rounded-xl lg:absolute lg:z-50 lg:p-2 lg:[--y-close:25%] lg:shadow-lg"
              >
                <a
                  href="#"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Bedrijfsbos
                </a>
                <a
                  href="#"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Schoolbos
                </a>
                <a
                  href="#"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Voedselbos
                </a>
                <a
                  href="#"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Natuuropwaardering
                </a>
                <a
                  href="#"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Dakboerderij
                </a>
                <a
                  href="#"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Start2Forest
                </a>
                <a
                  href="#"
                  className="block py-3 text-center lg:px-4 lg:py-2 lg:text-left text-text-primary hover:text-link hover:bg-background-primary transition-colors rounded-lg"
                >
                  Boscompensatie
                </a>
              </motion.nav>
            </AnimatePresence>
          </div>
          <a
            href="#"
            className="block py-3 text-md first:pt-7 lg:px-4 lg:py-2 lg:text-base first:lg:pt-2 text-text-primary hover:text-link transition-colors"
          >
            Visie
          </a>
          <a
            href="#"
            className="block py-3 text-md first:pt-7 lg:px-4 lg:py-2 lg:text-base first:lg:pt-2 text-text-primary hover:text-link transition-colors"
          >
            Over ons
          </a>
          <a
            href="#"
            className="block py-3 text-md first:pt-7 lg:px-4 lg:py-2 lg:text-base first:lg:pt-2 text-text-primary hover:text-link transition-colors"
          >
            Blogs
          </a>
          <a
            href="#"
            className="block py-3 text-md first:pt-7 lg:px-4 lg:py-2 lg:text-base first:lg:pt-2 text-text-primary hover:text-link transition-colors"
          >
            Contact
          </a>
        </motion.div>
        <div className="hidden justify-self-end lg:block">
          <div className="relative inline-block mr-4">
            <div className="relative">
              <button
                className="px-6 py-2 bg-link- text-text-primary rounded-xl border border-border-primary flex items-center gap-2"
              >
                NL <RxChevronDown />
              </button>
              <div className="absolute top-full mt-2 w-full bg-background rounded-xl border border-border-primary overflow-hidden">
                <a href="#" className="block px-6 py-2 hover:bg-link-primary hover:text-text-alternative">EN</a>
                <a href="#" className="block px-6 py-2 hover:bg-link-primary hover:text-text-alternative">DE</a>
              </div>
            </div>
            
          </div>
          <Button className="px-6 py-2 bg-link-primary text-text-alternative rounded-xl" title="Let's talk!" size="sm">
            Let's talk!
          </Button>
          
        </div>
      </div>
    </section>
  );
}
